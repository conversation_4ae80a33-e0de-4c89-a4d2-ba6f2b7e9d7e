/**
 * OpenAIProvider Tests
 */

import { OpenAIProvider } from '../providers/OpenAIProvider';
import { OpenAIConfig, LLMRequestConfig } from '../interfaces';
import { Message } from '@/types';

// Mock OpenAI SDK
const mockOpenAI = {
  chat: {
    completions: {
      create: jest.fn(),
    },
  },
  models: {
    list: jest.fn(),
  },
};

jest.mock('openai', () => {
  return jest.fn().mockImplementation(() => mockOpenAI);
});

describe('OpenAIProvider', () => {
  let provider: OpenAIProvider;
  let config: OpenAIConfig;

  beforeEach(() => {
    config = {
      apiKey: 'test-api-key',
      baseUrl: 'https://api.openai.com/v1',
      timeout: 30000,
    };

    provider = new OpenAIProvider(config);

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize with correct properties', () => {
      expect(provider.name).toBe('openai');
      expect(provider.supportsStreaming).toBe(true);
      expect(provider.supportsTools).toBe(true);
      expect(provider.supportsVision).toBe(true);
      expect(provider.models).toContain('gpt-4o');
      expect(provider.models).toContain('gpt-3.5-turbo');
    });

    it('should configure correctly', () => {
      const newConfig = {
        apiKey: 'new-api-key',
        organization: 'test-org',
      };

      expect(() => {
        provider.configure(newConfig);
      }).not.toThrow();
    });
  });

  describe('model information', () => {
    it('should return model info for known models', () => {
      const modelInfo = provider.getModelInfo('gpt-4o');
      
      expect(modelInfo).toBeDefined();
      expect(modelInfo?.name).toBe('gpt-4o');
      expect(modelInfo?.displayName).toBe('GPT-4o');
      expect(modelInfo?.supportsTools).toBe(true);
      expect(modelInfo?.supportsVision).toBe(true);
    });

    it('should return undefined for unknown models', () => {
      const modelInfo = provider.getModelInfo('unknown-model');
      expect(modelInfo).toBeUndefined();
    });
  });

  describe('configuration validation', () => {
    it('should validate config successfully', async () => {
      mockOpenAI.models.list.mockResolvedValue({ data: [] });

      const isValid = await provider.validateConfig();
      expect(isValid).toBe(true);
      expect(mockOpenAI.models.list).toHaveBeenCalled();
    });

    it('should handle validation failure', async () => {
      mockOpenAI.models.list.mockRejectedValue(new Error('API Error'));

      const isValid = await provider.validateConfig();
      expect(isValid).toBe(false);
    });
  });

  describe('health check', () => {
    it('should return healthy status', async () => {
      mockOpenAI.models.list.mockResolvedValue({ data: [] });

      const health = await provider.healthCheck();

      expect(health.status).toBe('healthy');
      expect(health.latency).toBeGreaterThanOrEqual(0);
      expect(health.timestamp).toBeGreaterThan(0);
    });

    it('should return unhealthy status on error', async () => {
      mockOpenAI.models.list.mockRejectedValue(new Error('API Error'));

      const health = await provider.healthCheck();
      
      expect(health.status).toBe('unhealthy');
      expect(health.error).toBe('API Error');
    });
  });

  describe('chat functionality', () => {
    const messages: Message[] = [
      {
        id: '1',
        type: 'user',
        content: 'Hello, how are you?',
        timestamp: Date.now(),
      },
    ];

    const config: LLMRequestConfig = {
      model: 'gpt-4o',
      temperature: 0.7,
      maxTokens: 1000,
    };

    it('should perform chat request successfully', async () => {
      const mockResponse = {
        id: 'chatcmpl-test',
        model: 'gpt-4o',
        choices: [
          {
            message: {
              content: 'Hello! I am doing well, thank you for asking.',
              role: 'assistant',
            },
            finish_reason: 'stop',
          },
        ],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 15,
          total_tokens: 25,
        },
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse);

      const response = await provider.chat(messages, config);

      expect(response).toBeDefined();
      expect(response.id).toBe('chatcmpl-test');
      expect(response.content).toBe('Hello! I am doing well, thank you for asking.');
      expect(response.role).toBe('assistant');
      expect(response.model).toBe('gpt-4o');
      expect(response.usage?.totalTokens).toBe(25);
      expect(response.finishReason).toBe('stop');
    });

    it('should handle chat request with tools', async () => {
      const configWithTools: LLMRequestConfig = {
        ...config,
        tools: [
          {
            name: 'get_weather',
            description: 'Get weather information',
            parameters: {
              type: 'object',
              properties: {
                location: { type: 'string' },
              },
              required: ['location'],
            },
          },
        ],
      };

      const mockResponse = {
        id: 'chatcmpl-test',
        model: 'gpt-4o',
        choices: [
          {
            message: {
              content: null,
              role: 'assistant',
              tool_calls: [
                {
                  id: 'call_test',
                  function: {
                    name: 'get_weather',
                    arguments: '{"location": "New York"}',
                  },
                },
              ],
            },
            finish_reason: 'tool_calls',
          },
        ],
        usage: {
          prompt_tokens: 20,
          completion_tokens: 10,
          total_tokens: 30,
        },
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockResponse);

      const response = await provider.chat(messages, configWithTools);

      expect(response.toolCalls).toBeDefined();
      expect(response.toolCalls).toHaveLength(1);
      expect(response.toolCalls?.[0].name).toBe('get_weather');
      expect(response.toolCalls?.[0].arguments).toEqual({ location: 'New York' });
      expect(response.finishReason).toBe('tool_calls');
    });

    it('should handle API errors', async () => {
      const apiError = new Error('API Error');
      (apiError as any).status = 500;
      
      mockOpenAI.chat.completions.create.mockRejectedValue(apiError);

      await expect(provider.chat(messages, config)).rejects.toThrow();
    });

    it('should handle rate limit errors', async () => {
      const rateLimitError = new Error('Rate limit exceeded');
      (rateLimitError as any).status = 429;
      (rateLimitError as any).headers = { 'retry-after': '60' };
      
      mockOpenAI.chat.completions.create.mockRejectedValue(rateLimitError);

      await expect(provider.chat(messages, config)).rejects.toThrow('Rate limit exceeded');
    });
  });

  describe('streaming chat', () => {
    const messages: Message[] = [
      {
        id: '1',
        type: 'user',
        content: 'Tell me a story',
        timestamp: Date.now(),
      },
    ];

    const config: LLMRequestConfig = {
      model: 'gpt-4o',
      stream: true,
    };

    it('should handle streaming response', async () => {
      const mockStream = [
        {
          id: 'chatcmpl-test',
          choices: [
            {
              delta: { content: 'Once' },
              finish_reason: null,
            },
          ],
        },
        {
          id: 'chatcmpl-test',
          choices: [
            {
              delta: { content: ' upon' },
              finish_reason: null,
            },
          ],
        },
        {
          id: 'chatcmpl-test',
          choices: [
            {
              delta: { content: ' a time' },
              finish_reason: 'stop',
            },
          ],
        },
      ];

      // Mock async iterator
      const mockAsyncIterator = {
        [Symbol.asyncIterator]: async function* () {
          for (const chunk of mockStream) {
            yield chunk;
          }
        },
      };

      mockOpenAI.chat.completions.create.mockResolvedValue(mockAsyncIterator);

      const chunks = [];
      for await (const chunk of provider.chatStream(messages, config)) {
        chunks.push(chunk);
      }

      expect(chunks).toHaveLength(3);
      expect(chunks[0].delta.content).toBe('Once');
      expect(chunks[1].delta.content).toBe(' upon');
      expect(chunks[2].delta.content).toBe(' a time');
      expect(chunks[2].finishReason).toBe('stop');
    });
  });

  describe('tool calling', () => {
    it('should handle tool call', async () => {
      const toolCall = {
        id: 'call_test',
        name: 'get_weather',
        arguments: { location: 'New York' },
      };

      const result = await provider.callTool(toolCall);

      expect(result).toBeDefined();
      expect(result.id).toBe('call_test');
      expect(result.result).toContain('get_weather');
    });
  });
});
