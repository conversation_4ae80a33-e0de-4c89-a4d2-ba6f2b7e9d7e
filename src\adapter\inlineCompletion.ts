import * as vscode from 'vscode';
import { getOpenAIClient } from '../common/openai';
import { getAgentConfig } from '../common/config';
import { retrieveContext } from '../RAG/retriever';
import { performance } from 'perf_hooks';

/**
 * A more advanced InlineCompletionProvider.
 * It uses RAG to retrieve context from the workspace and provides more intelligent completions.
 */
export function registerInlineCompletions(context: vscode.ExtensionContext) {
    const provider: vscode.InlineCompletionItemProvider = {
        async provideInlineCompletionItems(document, position, contextToken, _token) {
            
            const start = performance.now();

            const textBeforeCursor = document.getText(new vscode.Range(new vscode.Position(0, 0), position));
            const textAfterCursor = document.getText(new vscode.Range(position, new vscode.Position(document.lineCount, document.getText().length)));

            // Basic check to avoid triggering on empty lines or simple whitespace
            if (textBeforeCursor.trim() === '') {
                return [];
            }
            
            // Debounce logic can be handled by VS Code's request management, but we can add our own if needed.

            try {
                // 1. Retrieve context using RAG
                const retrievedChunks = await retrieveContext(textBeforeCursor, context);
                let contextPrompt = '';
                if (retrievedChunks.length > 0) {
                    contextPrompt = "Here is some relevant context from other files:\n\n---\n";
                    contextPrompt += retrievedChunks.map(c => `File: ${c.filePath}\n\n${c.content}`).join('\n\n---\n');
                }

                // 2. Construct a more sophisticated prompt
                const maxPrefixLength = 2000; // Limit the prefix length
                const truncatedPrefix = textBeforeCursor.slice(-maxPrefixLength);

                const prompt = `You are an expert AI code completion assistant.
Your task is to complete the code snippet provided by the user.
Do not output any explanation, markdown, or any text other than the code completion itself.
${contextPrompt}

Complete the following code snippet:
---
${truncatedPrefix}
---
`;
                const openai = await getOpenAIClient(context);
                const cfg = await getAgentConfig(context);

                const completion = await openai.chat.completions.create({
                    model: cfg.model,
                    temperature: cfg.temperature,
                    messages: [
                        { role: 'system', content: prompt }
                    ],
                    max_tokens: 128, // Increased token limit for multi-line completions
                    stop: ["\n\n", "---"] // Stop generation at double newlines or our separator
                });
                
                let completionText = completion.choices[0].message.content || '';

                if (!completionText.trim()) {
                    return [];
                }
                
                // 3. Clean up the completion text
                completionText = completionText.trim();
                // Remove potential markdown code block fences
                if (completionText.startsWith("```")) {
                    completionText = completionText.replace(/^```[a-zA-Z]*\n/, '').replace(/```$/, '');
                }

                if (!completionText) {
                    return [];
                }
                
                const item = new vscode.InlineCompletionItem(completionText, new vscode.Range(position, position));
                
                const end = performance.now();
                console.log(`Inline completion took ${end - start} ms.`);

                return [item];

            } catch (e) {
                console.error('[inline completion] error', e);
                return [];
            }
        }
    };

    context.subscriptions.push(
        vscode.languages.registerInlineCompletionItemProvider({ scheme: 'file', pattern: '**' }, provider)
    );
} 