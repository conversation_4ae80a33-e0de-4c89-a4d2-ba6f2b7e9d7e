import * as vscode from 'vscode';

export interface AgentConfig {
    apiKey: string;
    baseUrl: string;
    model: string;
    temperature: number;
}

/**
 * 读取用户设置，如果 apiKey 为空则尝试从 secret storage 中获取。
 */
export async function getAgentConfig(context: vscode.ExtensionContext): Promise<AgentConfig> {
    const cfg = vscode.workspace.getConfiguration('ai-agent');

    let apiKey = cfg.get<string>('apiKey', '');

    if (!apiKey) {
        // 从 SecretStorage 尝试读取
        apiKey = await context.secrets.get('ai-agent.apiKey') || '';
    }

    // 如果依旧为空，则提示用户输入
    if (!apiKey) {
        apiKey = await vscode.window.showInputBox({
            prompt: '请输入 OpenAI / DeepSeek 等兼容接口的 API Key',
            ignoreFocusOut: true,
            password: true,
            placeHolder: 'sk-...'
        }) || '';
        if (apiKey) {
            // 询问是否保存
            const shouldSave = await vscode.window.showQuickPick(['是', '否'], { placeHolder: '是否将 API Key 保存在本地 Secret Storage？' });
            if (shouldSave === '是') {
                await context.secrets.store('ai-agent.apiKey', apiKey);
            }
        }
    }

    const baseUrl = cfg.get<string>('baseUrl', 'https://api.openai.com/v1');
    const model = cfg.get<string>('model', 'gpt-4o');
    const temperature = cfg.get<number>('temperature', 0.2);

    return { apiKey, baseUrl, model, temperature };
} 