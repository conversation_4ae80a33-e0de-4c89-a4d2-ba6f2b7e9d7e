import * as vscode from 'vscode';
import { TextDecoder } from 'util';
import { createHash } from 'crypto';
import { getOpenAIClient } from '../openai';
import { EmbeddedChunk } from './types';

const CHUNK_SIZE = 200; // lines per chunk
const CACHE_DIR_NAME = '.ai_agent_cache';
const CACHE_FILE_NAME = 'workspace_embeddings.json';

/**
 * Splits a file's content into manageable chunks.
 * A simple line-based chunking strategy.
 * @param content The content of the file.
 * @returns An array of string chunks.
 */
function chunkFile(content: string): string[] {
    const lines = content.split('\n');
    const chunks: string[] = [];
    for (let i = 0; i < lines.length; i += CHUNK_SIZE) {
        chunks.push(lines.slice(i, i + CHUNK_SIZE).join('\n'));
    }
    return chunks;
}

/**
 * Indexes the entire workspace, creating vector embeddings for file chunks.
 * Displays progress in the status bar.
 */
export async function indexWorkspace(context: vscode.ExtensionContext) {
    await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "AI Agent: Indexing workspace...",
        cancellable: true
    }, async (progress, token) => {
        token.onCancellationRequested(() => {
            console.log("User canceled the indexing operation.");
        });

        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) {
            vscode.window.showInformationMessage("No workspace folder open.");
            return;
        }
        const workspaceRoot = workspaceFolders[0].uri;
        const cacheDirUri = vscode.Uri.joinPath(workspaceRoot, CACHE_DIR_NAME);
        const cacheFileUri = vscode.Uri.joinPath(cacheDirUri, CACHE_FILE_NAME);

        try {
            await vscode.workspace.fs.createDirectory(cacheDirUri);
        } catch (error) {
            // Directory likely already exists, ignore.
        }

        progress.report({ increment: 0, message: "Finding files..." });

        // TODO: Make include/exclude patterns configurable
        const files = await vscode.workspace.findFiles('**/*.{ts,js,py,go,java,html,css,md}', '**/node_modules/**');
        const totalFiles = files.length;
        if (totalFiles === 0) {
            vscode.window.showInformationMessage("No files found to index in the workspace.");
            return;
        }

        const openai = await getOpenAIClient(context);
        const allChunks: EmbeddedChunk[] = [];

        for (const [index, file] of files.entries()) {
            if (token.isCancellationRequested) break;

            const progressPercentage = (index / totalFiles) * 100;
            progress.report({ increment: (1 / totalFiles) * 100, message: `Processing ${file.fsPath.split('/').pop()}` });

            const contentBytes = await vscode.workspace.fs.readFile(file);
            const content = new TextDecoder().decode(contentBytes);
            const chunks = chunkFile(content);

            for (const chunkContent of chunks) {
                if (!chunkContent.trim()) continue;

                try {
                    const response = await openai.embeddings.create({
                        model: 'text-embedding-3-small', // Recommended model for cost/performance
                        input: chunkContent,
                    });

                    const embedding = response.data[0].embedding;
                    const chunkId = createHash('sha256').update(file.fsPath + chunkContent).digest('hex');
                    
                    allChunks.push({
                        id: chunkId,
                        filePath: vscode.workspace.asRelativePath(file),
                        content: chunkContent,
                        embedding: embedding
                    });
                } catch(e) {
                    console.error(`Failed to create embedding for a chunk in ${file.fsPath}`, e);
                    vscode.window.showWarningMessage(`Skipping a chunk in ${file.fsPath} due to an embedding error.`);
                }
            }
        }
        
        if (token.isCancellationRequested) {
            vscode.window.showInformationMessage("Workspace indexing was canceled.");
            return;
        }
        
        progress.report({ message: "Saving embeddings..." });
        const cacheContent = new TextEncoder().encode(JSON.stringify(allChunks, null, 2));
        await vscode.workspace.fs.writeFile(cacheFileUri, cacheContent);

        progress.report({ increment: 100, message: "Workspace indexing complete!" });
        vscode.window.showInformationMessage(`AI Agent: Indexed ${totalFiles} files, creating ${allChunks.length} embeddings.`);
    });
} 