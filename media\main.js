// @ts-ignore

const markdownit = require('markdown-it');

// This script will be run within the webview itself
(function () {
    const vscode = acquireVsCodeApi();
    const messagesContainer = document.getElementById('messages');
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-button');

    const md = window.markdownit({
        highlight: function (str, lang) {
            if (lang && hljs.getLanguage(lang)) {
                try {
                    return '<pre class="hljs"><code>' +
                           hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
                           '</code></pre>';
                } catch (__) {}
            }
    
            return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + '</code></pre>';
        }
    });

    let streamingMessageDiv = null;
    let streamingMessageContent = '';

    function processCodeBlocks(messageDiv) {
        const codeBlocks = messageDiv.querySelectorAll('pre');
        codeBlocks.forEach(pre => {
            const code = pre.querySelector('code');
            if (!code) return;

            const actionsContainer = document.createElement('div');
            actionsContainer.className = 'code-actions';

            const copyButton = document.createElement('button');
            copyButton.className = 'code-action-button';
            copyButton.textContent = 'Copy';
            copyButton.onclick = () => {
                navigator.clipboard.writeText(code.textContent).then(() => {
                    copyButton.textContent = 'Copied!';
                    setTimeout(() => {
                        copyButton.textContent = 'Copy';
                    }, 2000);
                });
            };

            const insertButton = document.createElement('button');
            insertButton.className = 'code-action-button';
            insertButton.textContent = 'Insert';
            insertButton.onclick = () => {
                vscode.postMessage({
                    command: 'applyCode',
                    code: code.textContent
                });
            };

            actionsContainer.appendChild(copyButton);
            actionsContainer.appendChild(insertButton);
            pre.appendChild(actionsContainer);
        });
    }

    function addMessage(type, text) {
        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message', type);
        
        if (type === 'ai' || type === 'system') {
            messageDiv.innerHTML = md.render(text);
        } else {
            messageDiv.textContent = text;
        }

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        return messageDiv;
    }

    sendButton.addEventListener('click', () => {
        const text = userInput.value;
        userInput.value = '';
        vscode.postMessage({ command: 'sendMessage', text });
    });

    userInput.addEventListener('keydown', (event) => {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            sendButton.click();
        }
    });

    window.addEventListener('message', event => {
        const message = event.data;
        switch (message.command) {
            case 'addMessage':
                addMessage(message.type, message.text);
                break;
            case 'startStreamingMessage':
                streamingMessageContent = '';
                streamingMessageDiv = addMessage('ai', '...');
                break;
            case 'streamMessageChunk':
                if (streamingMessageDiv) {
                    streamingMessageContent += message.chunk;
                    streamingMessageDiv.innerHTML = md.render(streamingMessageContent);
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }
                break;
            case 'endStreamingMessage':
                if (streamingMessageDiv) {
                    processCodeBlocks(streamingMessageDiv);
                }
                streamingMessageDiv = null;
                streamingMessageContent = '';
                break;
            case 'startThinking':
                sendButton.disabled = true;
                userInput.disabled = true;
                break;
            case 'endStreaming': // Matches 'endThinking' from provider
                sendButton.disabled = false;
                userInput.disabled = false;
                userInput.focus();
                break;
        }
    });

    // Set initial welcome message
    const welcomeMessage = "Welcome to AI Agent! How can I help you today?";
    const welcomeDiv = document.createElement('div');
    welcomeDiv.className = 'message ai';
    welcomeDiv.innerHTML = md.render(welcomeMessage);
    if(messagesContainer.firstChild) {
        messagesContainer.replaceChild(welcomeDiv, messagesContainer.firstChild);
    } else {
        messagesContainer.appendChild(welcomeDiv);
    }

}()); 