import * as vscode from 'vscode';
import OpenAI from 'openai';
import { getOpenAIClient } from '../common/openai';
import { getAgentConfig } from '../common/config';

async function runAITransform(context: vscode.ExtensionContext, prompt: string) {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showInformationMessage('没有活动编辑器');
        return;
    }

    const selection = editor.selection;
    const code = selection.isEmpty
        ? editor.document.getText()
        : editor.document.getText(selection);

    if (!code.trim()) {
        vscode.window.showInformationMessage('未找到需要处理的代码');
        return;
    }

    const cfg = await getAgentConfig(context);
    const openai = await getOpenAIClient(context);

    const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
        { role: 'system', content: prompt },
        { role: 'user', content: `代码如下：\n\n\u0060\u0060\u0060\n${code}\n\u0060\u0060\u0060` }
    ];

    const completion = await openai.chat.completions.create({
        model: cfg.model,
        temperature: cfg.temperature,
        messages,
        max_tokens: 2048
    });

    const newCode = completion.choices[0].message.content || '';
    if (!newCode.trim()) {
        vscode.window.showWarningMessage('AI 未返回结果');
        return;
    }

    // 在新文档打开结果，保留原文件
    const doc = await vscode.workspace.openTextDocument({ content: newCode, language: editor.document.languageId });
    await vscode.window.showTextDocument(doc, { preview: false });
}

export function registerCodeActionCommands(context: vscode.ExtensionContext) {
    context.subscriptions.push(
        vscode.commands.registerCommand('ai-agent.refactor', () =>
            runAITransform(
                context,
                '你是一名经验丰富的软件工程师，请重构用户提供的代码以提高可读性、性能并遵循最佳实践。只返回完整代码，不要附带任何解释。'
            )
        )
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('ai-agent.explain', () =>
            runAITransform(
                context,
                '你是一名技术文档专家，请为用户提供的代码生成详细的中文解释和内联注释。保持原有代码结构，并在适当位置加入注释。'
            )
        )
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('ai-agent.addTests', () =>
            runAITransform(
                context,
                '你是一名测试开发工程师，请为用户提供的代码（JavaScript/TypeScript/Python 等）生成对应的单元测试，使用常见测试框架（如 Jest/pytest）。只输出测试代码文件内容。'
            )
        )
    );
} 