/**
 * Theme Configurations - 主题配置
 * 
 * 定义各种主题的颜色和样式配置
 */

import { ThemeConfig } from '../types';

// 基础配置
const baseConfig = {
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
  },
  typography: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    fontSize: {
      xs: '12px',
      sm: '14px',
      md: '16px',
      lg: '18px',
      xl: '20px',
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      bold: 600,
    },
  },
  borderRadius: {
    sm: '4px',
    md: '8px',
    lg: '12px',
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  },
};

// 浅色主题
export const lightTheme: ThemeConfig = {
  name: 'light',
  colors: {
    primary: '#3b82f6',
    secondary: '#6b7280',
    background: '#ffffff',
    surface: '#f9fafb',
    text: '#111827',
    textSecondary: '#6b7280',
    border: '#e5e7eb',
    accent: '#10b981',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },
  ...baseConfig,
};

// 深色主题
export const darkTheme: ThemeConfig = {
  name: 'dark',
  colors: {
    primary: '#60a5fa',
    secondary: '#9ca3af',
    background: '#0f172a',
    surface: '#1e293b',
    text: '#f1f5f9',
    textSecondary: '#94a3b8',
    border: '#334155',
    accent: '#34d399',
    success: '#34d399',
    warning: '#fbbf24',
    error: '#f87171',
    info: '#60a5fa',
  },
  ...baseConfig,
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.3)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3)',
  },
};

// 高对比度主题
export const highContrastTheme: ThemeConfig = {
  name: 'high-contrast',
  colors: {
    primary: '#0066cc',
    secondary: '#666666',
    background: '#ffffff',
    surface: '#f0f0f0',
    text: '#000000',
    textSecondary: '#333333',
    border: '#000000',
    accent: '#008000',
    success: '#008000',
    warning: '#ff8c00',
    error: '#cc0000',
    info: '#0066cc',
  },
  ...baseConfig,
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.8)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.8), 0 2px 4px -1px rgba(0, 0, 0, 0.6)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.8), 0 4px 6px -2px rgba(0, 0, 0, 0.6)',
  },
};

// VS Code主题（可选）
export const vscodeTheme: ThemeConfig = {
  name: 'dark',
  colors: {
    primary: '#007acc',
    secondary: '#cccccc',
    background: '#1e1e1e',
    surface: '#252526',
    text: '#cccccc',
    textSecondary: '#969696',
    border: '#3c3c3c',
    accent: '#0e639c',
    success: '#89d185',
    warning: '#ffcc02',
    error: '#f14c4c',
    info: '#75beff',
  },
  ...baseConfig,
  typography: {
    ...baseConfig.typography,
    fontFamily: 'Consolas, "Courier New", monospace',
  },
};

// 主题工具函数
export const getThemeConfig = (themeName: string): ThemeConfig => {
  switch (themeName) {
    case 'light':
      return lightTheme;
    case 'dark':
      return darkTheme;
    case 'high-contrast':
      return highContrastTheme;
    case 'vscode':
      return vscodeTheme;
    default:
      return darkTheme;
  }
};

// CSS变量生成器
export const generateCSSVariables = (theme: ThemeConfig): string => {
  const variables: string[] = [];

  // 颜色变量
  Object.entries(theme.colors).forEach(([key, value]) => {
    variables.push(`--color-${key}: ${value};`);
  });

  // 间距变量
  Object.entries(theme.spacing).forEach(([key, value]) => {
    variables.push(`--spacing-${key}: ${value};`);
  });

  // 字体变量
  variables.push(`--font-family: ${theme.typography.fontFamily};`);
  Object.entries(theme.typography.fontSize).forEach(([key, value]) => {
    variables.push(`--font-size-${key}: ${value};`);
  });
  Object.entries(theme.typography.fontWeight).forEach(([key, value]) => {
    variables.push(`--font-weight-${key}: ${value};`);
  });

  // 边框圆角变量
  Object.entries(theme.borderRadius).forEach(([key, value]) => {
    variables.push(`--border-radius-${key}: ${value};`);
  });

  // 阴影变量
  Object.entries(theme.shadows).forEach(([key, value]) => {
    variables.push(`--shadow-${key}: ${value};`);
  });

  return `:root {\n  ${variables.join('\n  ')}\n}`;
};

// 主题检测工具
export const detectSystemTheme = (): 'light' | 'dark' => {
  if (typeof window === 'undefined') return 'dark';
  
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
};

// 主题切换动画
export const createThemeTransition = (duration = 300) => {
  const style = document.createElement('style');
  style.textContent = `
    * {
      transition: background-color ${duration}ms ease,
                  color ${duration}ms ease,
                  border-color ${duration}ms ease,
                  box-shadow ${duration}ms ease !important;
    }
  `;
  
  document.head.appendChild(style);
  
  setTimeout(() => {
    document.head.removeChild(style);
  }, duration);
};

// 主题验证
export const validateTheme = (theme: Partial<ThemeConfig>): boolean => {
  const requiredColors = [
    'primary', 'secondary', 'background', 'surface', 
    'text', 'textSecondary', 'border', 'accent'
  ];
  
  if (!theme.colors) return false;
  
  return requiredColors.every(color => 
    color in theme.colors! && typeof theme.colors![color as keyof typeof theme.colors] === 'string'
  );
};

// 颜色工具函数
export const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

export const rgbToHex = (r: number, g: number, b: number): string => {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
};

export const lighten = (color: string, amount: number): string => {
  const rgb = hexToRgb(color);
  if (!rgb) return color;
  
  const { r, g, b } = rgb;
  const newR = Math.min(255, Math.round(r + (255 - r) * amount));
  const newG = Math.min(255, Math.round(g + (255 - g) * amount));
  const newB = Math.min(255, Math.round(b + (255 - b) * amount));
  
  return rgbToHex(newR, newG, newB);
};

export const darken = (color: string, amount: number): string => {
  const rgb = hexToRgb(color);
  if (!rgb) return color;
  
  const { r, g, b } = rgb;
  const newR = Math.max(0, Math.round(r * (1 - amount)));
  const newG = Math.max(0, Math.round(g * (1 - amount)));
  const newB = Math.max(0, Math.round(b * (1 - amount)));
  
  return rgbToHex(newR, newG, newB);
};
