import OpenAI from 'openai';
import * as vscode from 'vscode';
import { getAgentConfig, AgentConfig } from './config';

let cachedInstance: { cfgKey: string; client: OpenAI } | undefined;

export async function getOpenAIClient(context: vscode.ExtensionContext): Promise<OpenAI> {
    const cfg: AgentConfig = await getAgentConfig(context);

    const key = `${cfg.apiKey}_${cfg.baseUrl}`;
    if (cachedInstance && cachedInstance.cfgKey === key) {
        return cachedInstance.client;
    }

    const client = new OpenAI({
        apiKey: cfg.apiKey,
        baseURL: cfg.baseUrl
    });

    cachedInstance = { cfgKey: key, client };
    return client;
} 