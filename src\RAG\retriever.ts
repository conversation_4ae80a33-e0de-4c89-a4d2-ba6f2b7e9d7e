import * as vscode from 'vscode';
import { TextDecoder } from 'util';
import { getOpenAIClient } from '../openai';
import { EmbeddedChunk } from './types';

const CACHE_DIR_NAME = '.ai_agent_cache';
const CACHE_FILE_NAME = 'workspace_embeddings.json';
const TOP_K = 5; // Number of chunks to retrieve

/**
 * Calculates the cosine similarity between two vectors.
 * @param vecA The first vector.
 * @param vecB The second vector.
 * @returns The cosine similarity score.
 */
function cosineSimilarity(vecA: number[], vecB: number[]): number {
    let dotProduct = 0;
    let magA = 0;
    let magB = 0;
    for (let i = 0; i < vecA.length; i++) {
        dotProduct += vecA[i] * vecB[i];
        magA += vecA[i] * vecA[i];
        magB += vecB[i] * vecB[i];
    }
    magA = Math.sqrt(magA);
    magB = Math.sqrt(magB);

    if (magA === 0 || magB === 0) {
        return 0;
    }
    return dotProduct / (magA * magB);
}

/**
 * Retrieves the most relevant context chunks from the cache based on a query.
 * @param query The user's query.
 * @param context The extension context.
 * @returns A promise that resolves to an array of the top K most relevant chunks.
 */
export async function retrieveContext(query: string, context: vscode.ExtensionContext): Promise<EmbeddedChunk[]> {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders) {
        return [];
    }
    const workspaceRoot = workspaceFolders[0].uri;
    const cacheFileUri = vscode.Uri.joinPath(workspaceRoot, CACHE_DIR_NAME, CACHE_FILE_NAME);

    let allChunks: EmbeddedChunk[] = [];
    try {
        const fileContent = await vscode.workspace.fs.readFile(cacheFileUri);
        allChunks = JSON.parse(new TextDecoder().decode(fileContent));
    } catch (error) {
        vscode.window.showInformationMessage("Workspace not indexed. Please run 'AI Agent: Index Workspace' first.");
        return [];
    }

    if (allChunks.length === 0) {
        return [];
    }

    const openai = await getOpenAIClient(context);

    // 1. Create embedding for the query
    const queryEmbeddingResponse = await openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: query,
    });
    const queryEmbedding = queryEmbeddingResponse.data[0].embedding;

    // 2. Calculate similarity for each chunk
    const chunksWithSimilarity = allChunks.map(chunk => ({
        ...chunk,
        similarity: cosineSimilarity(queryEmbedding, chunk.embedding)
    }));

    // 3. Sort by similarity and take top K
    chunksWithSimilarity.sort((a, b) => b.similarity - a.similarity);

    return chunksWithSimilarity.slice(0, TOP_K);
} 