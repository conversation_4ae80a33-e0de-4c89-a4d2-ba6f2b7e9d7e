/**
 * LLM Manager - LLM管理器
 * 
 * 负责管理多个LLM提供者，提供统一的接口和智能路由
 */

import { EventBus } from '@/core/EventBus';
import { 
  ILLMManager, 
  ILLMProvider, 
  LLMRequestConfig, 
  LLMResponse, 
  LLMStreamChunk, 
  ModelRequirements,
  UsageStats,
  HealthStatus,
  LLMError
} from './interfaces';
import { Message, ToolCall, ToolResult, LLMProvider } from '@/types';

interface RequestMetrics {
  startTime: number;
  endTime?: number;
  tokens?: number;
  cost?: number;
  success: boolean;
  error?: string;
}

export class LLMManager implements ILLMManager {
  private providers: Map<LLMProvider, ILLMProvider> = new Map();
  private eventBus: EventBus;
  private metrics: RequestMetrics[] = [];
  private maxMetricsHistory = 1000;

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
  }

  // 提供者管理
  registerProvider(provider: ILLMProvider): void {
    this.providers.set(provider.name, provider);
    
    this.eventBus.emit({
      type: 'llm.provider_registered',
      source: 'LLMManager',
      provider: provider.name,
      models: provider.models,
      capabilities: {
        streaming: provider.supportsStreaming,
        tools: provider.supportsTools,
        vision: provider.supportsVision,
      },
    });
  }

  unregisterProvider(name: LLMProvider): void {
    const removed = this.providers.delete(name);
    
    if (removed) {
      this.eventBus.emit({
        type: 'llm.provider_unregistered',
        source: 'LLMManager',
        provider: name,
      });
    }
  }

  getProvider(name: LLMProvider): ILLMProvider | undefined {
    return this.providers.get(name);
  }

  listProviders(): LLMProvider[] {
    return Array.from(this.providers.keys());
  }

  // 聊天功能
  async chat(
    provider: LLMProvider,
    messages: Message[],
    config: LLMRequestConfig
  ): Promise<LLMResponse> {
    const llmProvider = this.getProvider(provider);
    if (!llmProvider) {
      throw new LLMError(`Provider ${provider} not found`, provider, 'PROVIDER_NOT_FOUND');
    }

    const metrics: RequestMetrics = {
      startTime: Date.now(),
      success: false,
    };

    try {
      // 发布请求开始事件
      await this.eventBus.emit({
        type: 'llm.request_started',
        source: 'LLMManager',
        provider,
        model: config.model,
        messageCount: messages.length,
      });

      const response = await llmProvider.chat(messages, config);

      // 更新指标
      metrics.endTime = Date.now();
      metrics.tokens = response.usage?.totalTokens;
      metrics.cost = this.calculateCost(provider, config.model, response.usage);
      metrics.success = true;

      // 发布请求完成事件
      await this.eventBus.emit({
        type: 'llm.request_completed',
        source: 'LLMManager',
        provider,
        model: config.model,
        duration: metrics.endTime - metrics.startTime,
        tokens: metrics.tokens,
        cost: metrics.cost,
      });

      return response;
    } catch (error) {
      metrics.endTime = Date.now();
      metrics.error = (error as Error).message;

      // 发布请求失败事件
      await this.eventBus.emit({
        type: 'llm.request_failed',
        source: 'LLMManager',
        provider,
        model: config.model,
        error: metrics.error,
        duration: metrics.endTime - metrics.startTime,
      });

      throw error;
    } finally {
      this.addMetrics(metrics);
    }
  }

  async* chatStream(
    provider: LLMProvider,
    messages: Message[],
    config: LLMRequestConfig
  ): AsyncIterable<LLMStreamChunk> {
    const llmProvider = this.getProvider(provider);
    if (!llmProvider) {
      throw new LLMError(`Provider ${provider} not found`, provider, 'PROVIDER_NOT_FOUND');
    }

    const metrics: RequestMetrics = {
      startTime: Date.now(),
      success: false,
    };

    try {
      // 发布流式请求开始事件
      await this.eventBus.emit({
        type: 'llm.stream_started',
        source: 'LLMManager',
        provider,
        model: config.model,
        messageCount: messages.length,
      });

      let totalTokens = 0;
      
      for await (const chunk of llmProvider.chatStream(messages, config)) {
        if (chunk.usage) {
          totalTokens = chunk.usage.totalTokens;
        }
        
        // 发布流式块事件
        await this.eventBus.emit({
          type: 'llm.stream_chunk',
          source: 'LLMManager',
          provider,
          model: config.model,
          chunkId: chunk.id,
          hasContent: !!chunk.delta.content,
          hasToolCalls: !!chunk.delta.toolCalls,
        });

        yield chunk;
      }

      // 更新指标
      metrics.endTime = Date.now();
      metrics.tokens = totalTokens;
      metrics.cost = this.calculateCost(provider, config.model, { totalTokens } as any);
      metrics.success = true;

      // 发布流式完成事件
      await this.eventBus.emit({
        type: 'llm.stream_completed',
        source: 'LLMManager',
        provider,
        model: config.model,
        duration: metrics.endTime - metrics.startTime,
        tokens: metrics.tokens,
        cost: metrics.cost,
      });
    } catch (error) {
      metrics.endTime = Date.now();
      metrics.error = (error as Error).message;

      // 发布流式失败事件
      await this.eventBus.emit({
        type: 'llm.stream_failed',
        source: 'LLMManager',
        provider,
        model: config.model,
        error: metrics.error,
        duration: metrics.endTime - metrics.startTime,
      });

      throw error;
    } finally {
      this.addMetrics(metrics);
    }
  }

  // 工具调用
  async executeToolCall(toolCall: ToolCall, context?: any): Promise<ToolResult> {
    try {
      // 发布工具调用开始事件
      await this.eventBus.emit({
        type: 'llm.tool_call_started',
        source: 'LLMManager',
        toolName: toolCall.name,
        toolCallId: toolCall.id,
      });

      // 这里应该调用实际的工具执行逻辑
      // 暂时返回一个模拟结果
      const result: ToolResult = {
        id: toolCall.id,
        result: `Tool ${toolCall.name} executed successfully`,
      };

      // 发布工具调用完成事件
      await this.eventBus.emit({
        type: 'llm.tool_call_completed',
        source: 'LLMManager',
        toolName: toolCall.name,
        toolCallId: toolCall.id,
        success: true,
      });

      return result;
    } catch (error) {
      // 发布工具调用失败事件
      await this.eventBus.emit({
        type: 'llm.tool_call_failed',
        source: 'LLMManager',
        toolName: toolCall.name,
        toolCallId: toolCall.id,
        error: (error as Error).message,
      });

      throw error;
    }
  }

  // 模型选择
  selectBestModel(requirements: ModelRequirements): {
    provider: LLMProvider;
    model: string;
  } | null {
    const candidates: Array<{
      provider: LLMProvider;
      model: string;
      score: number;
    }> = [];

    for (const [providerName, provider] of this.providers) {
      for (const modelName of provider.models) {
        const modelInfo = provider.getModelInfo(modelName);
        if (!modelInfo) continue;

        let score = 0;

        // 检查必需的功能
        if (requirements.supportsTools && !modelInfo.supportsTools) continue;
        if (requirements.supportsVision && !modelInfo.supportsVision) continue;
        if (requirements.maxTokens && modelInfo.maxTokens < requirements.maxTokens) continue;

        // 计算分数
        if (requirements.costSensitive) {
          score += (1 / (modelInfo.inputCost + modelInfo.outputCost)) * 100;
        }

        if (requirements.speedSensitive) {
          // 假设更小的模型更快
          score += (1 / modelInfo.maxTokens) * 1000;
        }

        if (requirements.qualityLevel) {
          const qualityScores = {
            basic: modelInfo.maxTokens < 2000 ? 100 : 50,
            standard: modelInfo.maxTokens >= 2000 && modelInfo.maxTokens < 8000 ? 100 : 70,
            premium: modelInfo.maxTokens >= 8000 ? 100 : 60,
          };
          score += qualityScores[requirements.qualityLevel];
        }

        candidates.push({
          provider: providerName,
          model: modelName,
          score,
        });
      }
    }

    if (candidates.length === 0) {
      return null;
    }

    // 返回得分最高的模型
    candidates.sort((a, b) => b.score - a.score);
    const best = candidates[0];

    return {
      provider: best.provider,
      model: best.model,
    };
  }

  // 统计和监控
  getUsageStats(): UsageStats {
    const successfulMetrics = this.metrics.filter(m => m.success);
    const totalRequests = this.metrics.length;
    const totalTokens = successfulMetrics.reduce((sum, m) => sum + (m.tokens || 0), 0);
    const totalCost = successfulMetrics.reduce((sum, m) => sum + (m.cost || 0), 0);
    
    const requestsByProvider: Record<LLMProvider, number> = {} as any;
    const tokensByProvider: Record<LLMProvider, number> = {} as any;
    const costByProvider: Record<LLMProvider, number> = {} as any;

    // 这里需要从事件历史中统计，暂时返回空对象
    
    const durations = successfulMetrics
      .filter(m => m.endTime)
      .map(m => m.endTime! - m.startTime);
    
    const averageLatency = durations.length > 0 
      ? durations.reduce((sum, d) => sum + d, 0) / durations.length 
      : 0;

    const errorRate = totalRequests > 0 
      ? (totalRequests - successfulMetrics.length) / totalRequests 
      : 0;

    return {
      totalRequests,
      totalTokens,
      totalCost,
      requestsByProvider,
      tokensByProvider,
      costByProvider,
      averageLatency,
      errorRate,
    };
  }

  async getHealthStatus(): Promise<Record<LLMProvider, HealthStatus>> {
    const status: Record<LLMProvider, HealthStatus> = {} as any;

    for (const [providerName, provider] of this.providers) {
      try {
        status[providerName] = await provider.healthCheck();
      } catch (error) {
        status[providerName] = {
          status: 'unhealthy',
          error: (error as Error).message,
          timestamp: Date.now(),
        };
      }
    }

    return status;
  }

  // 私有方法
  private calculateCost(
    provider: LLMProvider, 
    model: string, 
    usage?: { promptTokens: number; completionTokens: number; totalTokens: number }
  ): number {
    if (!usage) return 0;

    const llmProvider = this.getProvider(provider);
    const modelInfo = llmProvider?.getModelInfo(model);
    
    if (!modelInfo) return 0;

    const inputCost = (usage.promptTokens / 1000) * modelInfo.inputCost;
    const outputCost = (usage.completionTokens / 1000) * modelInfo.outputCost;
    
    return inputCost + outputCost;
  }

  private addMetrics(metrics: RequestMetrics): void {
    this.metrics.push(metrics);
    
    // 限制历史记录大小
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics.shift();
    }
  }

  // 清理资源
  dispose(): void {
    this.providers.clear();
    this.metrics = [];
  }
}
