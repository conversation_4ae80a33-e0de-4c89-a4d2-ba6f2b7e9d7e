import * as vscode from 'vscode';
import { registerInlineCompletions } from './adapter/inlineCompletion';
import { registerCodeActionCommands } from './adapter/codeActions';
import { ChatViewProvider } from './ChatViewProvider';
import { indexWorkspace } from './RAG/indexer';

export function activate(context: vscode.ExtensionContext) {

	console.log('Congratulations, your extension "ai-agent" is now active!');

	// 注册 Chat View Provider
	const chatProvider = new ChatViewProvider(context);
	context.subscriptions.push(
		vscode.window.registerWebviewViewProvider(ChatViewProvider.viewType, chatProvider)
	);

	// 注册 Code Action 命令
	registerCodeActionCommands(context);
	
	// 注册 inline completion
	registerInlineCompletions(context);

	// 注册索引命令
	context.subscriptions.push(
		vscode.commands.registerCommand('ai-agent.indexWorkspace', () => {
			indexWorkspace(context);
		})
	);
}


export function deactivate() {} 