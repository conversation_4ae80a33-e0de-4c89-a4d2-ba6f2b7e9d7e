import * as vscode from 'vscode';
import OpenAI from 'openai';
import * as path from 'path';
import { TextDecoder, TextEncoder } from 'util';
import { getOpenAIClient } from './common/openai';
import { getAgentConfig } from './common/config';
import { retrieveContext } from './RAG/retriever';
import { getNonce } from './core/utils';
import { ChatCompletionMessageToolCall } from 'openai/resources/chat/completions';

const SYSTEM_PROMPT = `You are an expert AI programmer who is helping a user with their coding tasks.
You have access to a set of tools to help the user. The user is in a VS Code environment.
When you need to use a tool, respond with a JSON object in the following format. Do not add any extra explanation.
{
  "type": "tool_use",
  "tool_name": "tool_name_here",
  "tool_input": { ... }
}
`;


export class ChatViewProvider implements vscode.WebviewViewProvider {

	public static readonly viewType = 'aiAgentChatView';

	private _view?: vscode.WebviewView;
	private _conversationHistory: OpenAI.Chat.ChatCompletionMessageParam[] = [];

	constructor(
		private readonly _context: vscode.ExtensionContext,
	) { }

	public resolveWebviewView(
		webviewView: vscode.WebviewView,
		context: vscode.WebviewViewResolveContext,
		_token: vscode.CancellationToken,
	) {
		this._view = webviewView;

		webviewView.webview.options = {
			// Allow scripts in the webview
			enableScripts: true,

			localResourceRoots: [
				this._context.extensionUri
			]
		};

		webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

		webviewView.webview.onDidReceiveMessage(async data => {
			switch (data.command) {
				case 'sendMessage':
                    this._view?.webview.postMessage({ command: 'addMessage', type: 'user', text: data.text });
                    this._conversationHistory.push({ role: 'user', content: data.text });
					await this.runConversation();
					break;
                case 'applyCode':
                    const editor = vscode.window.activeTextEditor;
                    if (editor) {
                        editor.edit(editBuilder => {
                            if (editor.selection.isEmpty) {
                                editBuilder.insert(editor.selection.active, data.code);
                            } else {
                                editBuilder.replace(editor.selection, data.code);
                            }
                        });
                    }
                    return;
			}
		});
	}

    private async runConversation() {
        if (!this._view) return;
        this._view.webview.postMessage({ command: 'startThinking' });

        try {
            const assistantMessageForHistory: OpenAI.Chat.ChatCompletionMessageParam = {
                role: 'assistant',
                content: null,
                tool_calls: []
            };

            const assistantResponse = await this.streamAndProcessFirstResponse(assistantMessageForHistory);

            if (assistantResponse.tool_calls && assistantResponse.tool_calls.length > 0) {
                assistantMessageForHistory.tool_calls = assistantResponse.tool_calls;
                assistantMessageForHistory.content = assistantResponse.content;
                this._conversationHistory.push(assistantMessageForHistory);

                const toolResults = await Promise.all(
                    assistantResponse.tool_calls.map(tc => this.executeToolCall(tc))
                );
                this._conversationHistory.push(...toolResults);

                const finalResponse = await this.streamAndProcessFinalResponse();
                this._conversationHistory.push(finalResponse);
            } else {
                assistantMessageForHistory.content = assistantResponse.content;
                assistantMessageForHistory.tool_calls = undefined; // No tool calls, so remove the property
                this._conversationHistory.push(assistantMessageForHistory);
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
            this._view?.webview.postMessage({ command: 'addMessage', type: 'ai', text: `Sorry, I encountered an error: ${errorMessage}` });
        } finally {
            this._view?.webview.postMessage({ command: 'endStreaming' });
        }
    }

    private async streamAndProcessFirstResponse(
        assistantMessage: OpenAI.Chat.ChatCompletionMessageParam
    ): Promise<OpenAI.Chat.ChatCompletionAssistantMessageParam> {
        if(!this._view) { throw new Error("Webview not available"); }
        
        const userQuery = (this._conversationHistory[this._conversationHistory.length - 1] as OpenAI.Chat.ChatCompletionUserMessageParam).content as string;
        const retrievedChunks = await retrieveContext(userQuery, this._context);
        const contextPrompt = retrievedChunks.length > 0
            ? "Use the following context... \n" + retrievedChunks.map(c => `File: ${c.filePath}\n\n${c.content}`).join('\n\n---\n')
            : '';
        
        const messagesToProcess: OpenAI.Chat.ChatCompletionMessageParam[] = [
            { role: 'system', content: SYSTEM_PROMPT },
            ...(contextPrompt ? [{ role: 'system', content: contextPrompt } as const] : []),
            ...this._conversationHistory
        ];

        const openai = await getOpenAIClient(this._context);
        const cfg = await getAgentConfig(this._context);
        const stream = await openai.chat.completions.create({
            messages: messagesToProcess,
            model: cfg.model,
            stream: true,
            tools: [
                { type: 'function', function: { name: 'readFile', description: 'Read a file from the workspace', parameters: { type: 'object', properties: { path: { type: 'string', description: 'The path to the file.' } }, required: ['path'] } } },
                { type: 'function', function: { name: 'writeFile', description: 'Write to a file in the workspace', parameters: { type: 'object', properties: { path: { type: 'string', description: 'The path to the file.' }, content: { type: 'string', description: 'The content to write.' } }, required: ['path', 'content'] } } },
                { type: 'function', function: { name: 'runTerminal', description: 'Run a command in the terminal', parameters: { type: 'object', properties: { command: { type: 'string', description: 'The command to run.' } }, required: ['command'] } } }
            ],
            tool_choice: 'auto',
        });

        this._view.webview.postMessage({ command: 'startStreamingMessage' });
        let fullResponse = "";
        const toolCallAggregator = new Map<number, {id?: string, name?: string, arguments?: string}>();

        for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content;
            if (content) {
                fullResponse += content;
                if(assistantMessage.content !== null) {
                    assistantMessage.content += content;
                }
                this._view.webview.postMessage({ command: 'streamMessageChunk', chunk: content });
            }
            if (chunk.choices[0]?.delta?.tool_calls) {
                for (const tc of chunk.choices[0].delta.tool_calls) {
                    if (tc.index === undefined) continue;
                    let existing = toolCallAggregator.get(tc.index);
                    if (!existing) {
                        existing = {};
                        toolCallAggregator.set(tc.index, existing);
                    }
                    if (tc.id) existing.id = tc.id;
                    if (tc.function?.name) existing.name = tc.function.name;
                    if (tc.function?.arguments) {
                        if (!existing.arguments) existing.arguments = "";
                        existing.arguments += tc.function.arguments;
                    }
                }
            }
        }
        this._view.webview.postMessage({ command: 'endStreamingMessage' });

        const finalToolCalls: ChatCompletionMessageToolCall[] = Array.from(toolCallAggregator.values())
            .filter(tc => tc.id && tc.name && tc.arguments)
            .map(tc => ({ id: tc.id!, type: 'function', function: { name: tc.name!, arguments: tc.arguments! } }));

        return {
            role: 'assistant',
            content: fullResponse || null,
            tool_calls: finalToolCalls.length > 0 ? finalToolCalls : undefined,
        };
    }

    private async streamAndProcessFinalResponse(): Promise<OpenAI.Chat.ChatCompletionAssistantMessageParam> {
        if(!this._view) { throw new Error("Webview not available"); }

        const messagesToProcess: OpenAI.Chat.ChatCompletionMessageParam[] = [
            { role: 'system', content: SYSTEM_PROMPT },
            ...this._conversationHistory,
        ];

        const openai = await getOpenAIClient(this._context);
        const cfg = await getAgentConfig(this._context);
        const stream = await openai.chat.completions.create({
            messages: messagesToProcess,
            model: cfg.model,
            stream: true,
        });

        this._view.webview.postMessage({ command: 'startStreamingMessage' });
        let fullResponse = "";
        for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content;
            if (content) {
                fullResponse += content;
                this._view.webview.postMessage({ command: 'streamMessageChunk', chunk: content });
            }
        }
        this._view.webview.postMessage({ command: 'endStreamingMessage' });

        return { role: 'assistant', content: fullResponse };
    }

    private async executeToolCall(toolCall: ChatCompletionMessageToolCall): Promise<OpenAI.Chat.ChatCompletionToolMessageParam> {
        const toolName = toolCall.function.name;
        this._view?.webview.postMessage({ command: 'addMessage', type: 'ai', text: `*Calling tool: ${toolName}...*`});
        
        let toolOutput = '';
        try {
            const toolInput = JSON.parse(toolCall.function.arguments);
            if (toolName === 'readFile') {
                const uri = vscode.Uri.file(path.join(vscode.workspace.rootPath || '', toolInput.path));
                const fileContent = await vscode.workspace.fs.readFile(uri);
                toolOutput = new TextDecoder().decode(fileContent);
            } else if (toolName === 'writeFile') {
                const uri = vscode.Uri.file(path.join(vscode.workspace.rootPath || '', toolInput.path));
                await vscode.workspace.fs.writeFile(uri, new TextEncoder().encode(toolInput.content));
                toolOutput = `Successfully wrote to ${toolInput.path}`;
            } else if (toolName === 'runTerminal') {
                const terminal = vscode.window.createTerminal(`AI Agent: ${toolInput.command}`);
                terminal.sendText(toolInput.command);
                terminal.show();
                toolOutput = `Executed command: ${toolInput.command}`;
            } else {
                toolOutput = `Unknown tool: ${toolName}`;
            }
        } catch (e: any) {
            toolOutput = `Error executing tool ${toolName}: ${e.message}`;
        }

        return {
            role: 'tool',
            tool_call_id: toolCall.id,
            content: toolOutput,
        };
    }

	private _getHtmlForWebview(webview: vscode.Webview) {
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._context.extensionUri, 'media', 'main.js'));
        const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._context.extensionUri, 'media', 'main.css'));
        const markdownItUri = webview.asWebviewUri(vscode.Uri.joinPath(this._context.extensionUri, 'node_modules', 'markdown-it', 'dist', 'markdown-it.min.js'));
        const highlightJsScriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._context.extensionUri, 'node_modules', 'highlight.js', 'lib', 'index.js'));
        const highlightJsStyleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._context.extensionUri, 'node_modules', 'highlight.js', 'styles', 'github-dark.css'));
    
        const nonce = getNonce();
    
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}' ${webview.cspSource};">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link href="${styleUri}" rel="stylesheet">
            <link href="${highlightJsStyleUri}" rel="stylesheet">
            <script nonce="${nonce}" src="${markdownItUri}"></script>
            <script nonce="${nonce}" src="${highlightJsScriptUri}"></script>
            <title>AI Agent</title>
        </head>
        <body>
            <div id="chat-container">
                <div id="messages">
                    <div class="message ai">Welcome to AI Agent! How can I help you today?</div>
                </div>
                <div id="input-area">
                    <input type="text" id="user-input" placeholder="Type your message here..." />
                    <button id="send-button">Send</button>
                </div>
            </div>
            <script nonce="${nonce}" src="${scriptUri}"></script>
        </body>
        </html>`;
    }
} 