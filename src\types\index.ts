// Core types for the AI Agent extension

export interface AppState {
  // System state
  system: SystemState;
  // User configuration
  config: ConfigState;
  // Conversation state
  conversation: ConversationState;
  // Project state
  project: ProjectState;
  // UI state
  ui: UIState;
}

export interface SystemState {
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  version: string;
  performance: PerformanceMetrics;
}

export interface ConfigState {
  apiKey: string;
  baseUrl: string;
  model: string;
  temperature: number;
  maxTokens: number;
  provider: LLMProvider;
}

export interface ConversationState {
  messages: Message[];
  isProcessing: boolean;
  currentContext: ConversationContext;
  history: ConversationHistory[];
}

export interface ProjectState {
  workspace: WorkspaceInfo;
  indexStatus: IndexStatus;
  files: FileInfo[];
  gitStatus: GitStatus;
}

export interface UIState {
  theme: Theme;
  layout: LayoutConfig;
  activePanel: string;
  sidebarCollapsed: boolean;
  preferences: UserPreferences;
}

// Message types
export interface Message {
  id: string;
  type: 'user' | 'assistant' | 'system' | 'tool';
  content: string | CodeBlock[];
  timestamp: number;
  metadata?: MessageMetadata;
  toolCalls?: ToolCall[];
}

export interface CodeBlock {
  language: string;
  code: string;
  filename?: string;
}

export interface MessageMetadata {
  tokens?: number;
  model?: string;
  duration?: number;
  context?: string[];
}

// LLM types
export type LLMProvider = 'openai' | 'claude' | 'gemini' | 'deepseek' | 'custom';

export interface LLMRequest {
  messages: Message[];
  model: string;
  temperature?: number;
  maxTokens?: number;
  tools?: Tool[];
  stream?: boolean;
}

export interface LLMResponse {
  content: string;
  toolCalls?: ToolCall[];
  usage?: TokenUsage;
  model: string;
}

export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
}

// Tool types
export interface Tool {
  name: string;
  description: string;
  parameters: ToolParameters;
}

export interface ToolParameters {
  type: 'object';
  properties: Record<string, ToolProperty>;
  required?: string[];
}

export interface ToolProperty {
  type: string;
  description: string;
  enum?: string[];
}

export interface ToolCall {
  id: string;
  name: string;
  arguments: Record<string, any>;
}

export interface ToolResult {
  id: string;
  result: any;
  error?: string;
}

// RAG types
export interface EmbeddedChunk {
  id: string;
  content: string;
  embedding: number[];
  metadata: ChunkMetadata;
}

export interface ChunkMetadata {
  filePath: string;
  fileType: string;
  language: string;
  symbols: string[];
  dependencies: string[];
  lastModified: number;
  startLine?: number;
  endLine?: number;
}

export interface SearchResult {
  chunk: EmbeddedChunk;
  similarity: number;
  relevance: number;
}

export interface ConversationContext {
  chunks: EmbeddedChunk[];
  totalTokens: number;
  sources: string[];
}

// File and workspace types
export interface FileInfo {
  path: string;
  name: string;
  type: string;
  size: number;
  lastModified: number;
  isIndexed: boolean;
}

export interface WorkspaceInfo {
  name: string;
  path: string;
  type: ProjectType;
  language: string[];
  dependencies: Dependency[];
}

export type ProjectType = 'typescript' | 'javascript' | 'python' | 'java' | 'go' | 'rust' | 'other';

export interface Dependency {
  name: string;
  version: string;
  type: 'production' | 'development';
}

export interface IndexStatus {
  isIndexing: boolean;
  progress: number;
  totalFiles: number;
  indexedFiles: number;
  lastIndexed: number;
}

export interface GitStatus {
  branch: string;
  hasChanges: boolean;
  staged: string[];
  unstaged: string[];
  untracked: string[];
}

// UI types
export type Theme = 'light' | 'dark' | 'high-contrast';

export interface LayoutConfig {
  sidebarWidth: number;
  chatHeight: number;
  fontSize: number;
}

export interface UserPreferences {
  language: string;
  autoSave: boolean;
  showLineNumbers: boolean;
  wordWrap: boolean;
  codeTheme: string;
}

// Event types
export interface BaseEvent {
  type: string;
  timestamp: number;
  source: string;
  id: string;
  [key: string]: any; // 允许额外的属性
}

export interface UserMessageEvent extends BaseEvent {
  type: 'user.message';
  message: string;
}

export interface AIResponseEvent extends BaseEvent {
  type: 'ai.response';
  response: string;
  toolCalls?: ToolCall[];
}

export interface FileChangeEvent extends BaseEvent {
  type: 'project.file_change';
  filePath: string;
  changeType: 'created' | 'modified' | 'deleted';
}

// Performance types
export interface PerformanceMetrics {
  memoryUsage: number;
  cpuUsage: number;
  responseTime: number;
  indexSize: number;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
  stack?: string;
}

// Conversation history
export interface ConversationHistory {
  id: string;
  title: string;
  messages: Message[];
  createdAt: number;
  updatedAt: number;
}
