import { getNonce } from '../utils';

describe('Core Utils', () => {
    
    it('getNonce should return a 32-character string', () => {
        const nonce = getNonce();
        expect(typeof nonce).toBe('string');
        expect(nonce.length).toBe(32);
    });

    it('getNonce should return a different string on each call', () => {
        const nonce1 = getNonce();
        const nonce2 = getNonce();
        expect(nonce1).not.toBe(nonce2);
    });

}); 