body {
    font-family: var(--vscode-font-family);
    color: var(--vscode-editor-foreground);
    background-color: var(--vscode-editor-background);
    padding: 1rem;
    height: 100vh;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

#chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

#messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 0.5rem;
    border: 1px solid var(--vscode-input-border);
    border-radius: 4px;
    margin-bottom: 1rem;
}

#input-area {
    display: flex;
}

#user-input {
    flex-grow: 1;
    margin-right: 0.5rem;
    border-radius: 4px;
    padding: 0.5rem;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border: 1px solid var(--vscode-input-border);
}

button {
    border-radius: 4px;
    padding: 0.5rem 1rem;
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: 1px solid var(--vscode-button-border);
    cursor: pointer;
}

button:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.message {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
}

.user {
    background-color: var(--vscode-list-activeSelectionBackground);
    text-align: right;
}

.ai {
    background-color: var(--vscode-list-inactiveSelectionBackground);
}

.code-button-container {
    position: relative;
    text-align: right;
    margin-top: -2.5em;
    margin-right: 0.5em;
    height: 2em;
}

.code-action-button {
    padding: 2px 6px;
    font-size: 0.8em;
    background-color: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
    border: 1px solid var(--vscode-button-border);
    cursor: pointer;
    border-radius: 4px;
}

.code-action-button:hover {
    background-color: var(--vscode-button-secondaryHoverBackground);
}

.message pre {
    position: relative;
    background-color: #2d2d2d;
    padding: 1em;
    border-radius: 5px;
    overflow-x: auto;
    font-family: var(--vscode-editor-font-family);
}

.code-actions {
    position: absolute;
    top: 5px;
    right: 5px;
    display: none;
}

.message pre:hover .code-actions {
    display: flex;
    gap: 5px;
}

.code-action-button {
    background-color: #3c3c3c;
    color: #cccccc;
    border: 1px solid #555555;
    border-radius: 3px;
    padding: 3px 8px;
    cursor: pointer;
    font-size: 12px;
}

.code-action-button:hover {
    background-color: #4c4c4c;
} 